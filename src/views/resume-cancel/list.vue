<template>
  <div class="box">
    <!-- 搜索筛选区域 -->
    <div id="top-container" class="pb-15">
      <el-card class="mb-15">
        <el-form ref="searchForm" :model="formData" label-width="120px" class="search-form">
          <el-row :gutter="20">
            <!-- 第一行：基础信息搜索 -->
            <el-col :span="6">
              <el-form-item label="注销日志ID">
                <el-input v-model="formData.cancelLogId" placeholder="请输入注销日志ID" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="手机号">
                <el-input v-model="formData.mobile" placeholder="请输入手机号" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="用户姓名">
                <el-input v-model="formData.name" placeholder="请输入用户姓名" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="邮箱">
                <el-input v-model="formData.email" placeholder="请输入邮箱" clearable />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 第二行：状态筛选 -->
            <el-col :span="6">
              <el-form-item label="注销状态">
                <el-select v-model="formData.status" placeholder="请选择注销状态" clearable>
                  <el-option
                    v-for="item in statusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="注销原因类型">
                <el-select
                  v-model="formData.cancelReasonType"
                  placeholder="请选择注销原因类型"
                  clearable
                >
                  <el-option
                    v-for="item in cancelReasonOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="短信状态">
                <el-select v-model="formData.smsStatus" placeholder="请选择短信状态" clearable>
                  <el-option
                    v-for="item in smsStatusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="操作管理员ID">
                <el-input v-model="formData.adminId" placeholder="请输入管理员ID" clearable />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 第三行：时间范围 -->
            <el-col :span="8">
              <el-form-item label="申请时间">
                <DatePickerRange
                  v-model:start="formData.applyTimeStart"
                  v-model:end="formData.applyTimeEnd"
                  placeholder="请选择申请时间范围"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="冷静期结束时间">
                <DatePickerRange
                  v-model:start="formData.cooldownEndTimeStart"
                  v-model:end="formData.cooldownEndTimeEnd"
                  placeholder="请选择冷静期结束时间范围"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="完成时间">
                <DatePickerRange
                  v-model:start="formData.completeTimeStart"
                  v-model:end="formData.completeTimeEnd"
                  placeholder="请选择完成时间范围"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 第四行：其他条件和操作按钮 -->
            <el-col :span="6">
              <el-form-item label="IP地址">
                <el-input v-model="formData.ip" placeholder="请输入IP地址" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="排序字段">
                <el-select v-model="formData.sortField" placeholder="请选择排序字段" clearable>
                  <el-option label="申请时间" value="apply_time" />
                  <el-option label="冷静期结束时间" value="cooldown_end_time" />
                  <el-option label="完成时间" value="complete_time" />
                  <el-option label="状态" value="status" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="排序方向">
                <el-select v-model="formData.sortOrder" placeholder="请选择排序方向" clearable>
                  <el-option label="升序" value="ASC" />
                  <el-option label="降序" value="DESC" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button @click="resetForm">重置</el-button>
                <el-button type="success" @click="exportData">导出</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 统计数据展示 -->
      <el-card class="mb-15" v-if="statistics">
        <div class="statistics-container">
          <div class="stat-item">
            <span class="stat-label">总申请数：</span>
            <span class="stat-value">{{ statistics.overview?.totalApplies || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">今日申请：</span>
            <span class="stat-value">{{ statistics.overview?.todayApplies || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">本周申请：</span>
            <span class="stat-value">{{ statistics.overview?.weekApplies || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">本月申请：</span>
            <span class="stat-value">{{ statistics.overview?.monthApplies || 0 }}</span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据列表 -->
    <el-table
      ref="table"
      :data="list"
      border
      :max-height="maxTableHeight"
      v-loading="loading"
      @sort-change="handleSortTable"
    >
      <el-table-column prop="id" label="注销日志ID" width="120" align="center" />
      <el-table-column label="用户信息" width="200" align="center">
        <template #default="{ row }">
          <div>
            <div>{{ row.name }}</div>
            <div class="text-gray-500 text-sm">{{ row.mobileMasked }}</div>
            <div class="text-gray-500 text-sm">{{ row.emailMasked }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="statusText" label="注销状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.status)" size="small">
            {{ row.statusText }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="cancelReasonTypeText"
        label="注销原因"
        width="200"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column prop="applyTimeFormat" label="申请时间" width="160" align="center" />
      <el-table-column
        prop="cooldownEndTimeFormat"
        label="冷静期结束时间"
        width="160"
        align="center"
      />
      <el-table-column label="剩余冷静期" width="120" align="center">
        <template #default="{ row }">
          <span :class="{ 'text-red-500': row.remainingCooldownDays <= 0 }">
            {{ row.remainingCooldownDays }}天
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="smsStatusText" label="短信状态" width="140" align="center" />
      <el-table-column prop="ip" label="IP地址" width="120" align="center" />
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="viewDetail(row.id)">查看详情</el-button>
          <el-button
            v-if="row.status === 1"
            size="small"
            type="danger"
            @click="manualCancel(row.id)"
          >
            人工执行注销
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Paging
      class="mt-15"
      @change="handlePaginationChange"
      :total="pagination.total"
      :page="pagination.page"
      :limit="pagination.limit"
    />

    <!-- 详情弹窗 -->
    <DetailDialog ref="detailDialog" @refresh="getList" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getResumeCancelList,
  getResumeCancelStatistics,
  getResumeCancelFilterOptions,
  manualCancelResume,
  exportResumeCancelList
} from '/@/api/resumeCancel'
import Paging from '/@/components/base/paging.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import DetailDialog from './components/DetailDialog.vue'

defineOptions({ name: 'resumeCancelList' })

interface FormData {
  cancelLogId: string
  mobile: string
  name: string
  email: string
  status: string
  cancelReasonType: string
  smsStatus: string
  applyTimeStart: string
  applyTimeEnd: string
  cooldownEndTimeStart: string
  cooldownEndTimeEnd: string
  completeTimeStart: string
  completeTimeEnd: string
  adminId: string
  ip: string
  sortField: string
  sortOrder: string
  page: number
  pageSize: number
}

const searchForm = ref()
const table = ref()
const detailDialog = ref()
const maxTableHeight = ref(450)

const loading = ref(false)
const list = ref([])
const statistics = ref(null)

// 筛选选项
const statusOptions = ref([])
const cancelReasonOptions = ref([])
const smsStatusOptions = ref([])

// 搜索表单数据
const formData = reactive<FormData>({
  cancelLogId: '',
  mobile: '',
  name: '',
  email: '',
  status: '',
  cancelReasonType: '',
  smsStatus: '',
  applyTimeStart: '',
  applyTimeEnd: '',
  cooldownEndTimeStart: '',
  cooldownEndTimeEnd: '',
  completeTimeStart: '',
  completeTimeEnd: '',
  adminId: '',
  ip: '',
  sortField: '',
  sortOrder: '',
  page: 1,
  pageSize: 20
})

// 分页数据
const pagination = reactive({
  total: 0,
  page: 1,
  limit: 20
})

// 获取列表数据
const getList = async () => {
  loading.value = true

  const params = {}
  Object.keys(formData).forEach((key: string) => {
    const value = formData[key]
    if (value !== '' && value !== null && value !== undefined) {
      params[key] = Array.isArray(value) ? value.join() : value
    }
  })

  try {
    const { list: dataList, pages } = await getResumeCancelList(params)
    list.value = dataList
    pagination.total = pages.total
    pagination.page = pages.page
    pagination.limit = pages.pageSize
  } catch (error) {
    console.error('获取注销申请列表失败:', error)
  } finally {
    loading.value = false
    getTableHeight()
  }
}

// 获取统计数据
const getStatistics = async () => {
  try {
    const data = await getResumeCancelStatistics({})
    statistics.value = data
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取筛选选项
const getFilterOptions = async () => {
  try {
    const data = await getResumeCancelFilterOptions()
    statusOptions.value = data.statusOptions || []
    cancelReasonOptions.value = data.cancelReasonOptions || []
    smsStatusOptions.value = data.smsStatusOptions || []
  } catch (error) {
    console.error('获取筛选选项失败:', error)
  }
}

// 搜索
const search = () => {
  formData.page = 1
  pagination.page = 1
  getList()
}

// 重置表单
const resetForm = () => {
  searchForm.value.resetFields()
  Object.assign(formData, {
    cancelLogId: '',
    mobile: '',
    name: '',
    email: '',
    status: '',
    cancelReasonType: '',
    smsStatus: '',
    applyTimeStart: '',
    applyTimeEnd: '',
    cooldownEndTimeStart: '',
    cooldownEndTimeEnd: '',
    completeTimeStart: '',
    completeTimeEnd: '',
    adminId: '',
    ip: '',
    sortField: '',
    sortOrder: '',
    page: 1,
    pageSize: 20
  })
  getList()
}

// 导出数据
const exportData = async () => {
  try {
    const params = {}
    Object.keys(formData).forEach((key: string) => {
      const value = formData[key]
      if (value !== '' && value !== null && value !== undefined) {
        params[key] = Array.isArray(value) ? value.join() : value
      }
    })

    await exportResumeCancelList(params)
    ElMessage.success('数据开始导出，成功下载后会在企业微信通知，请后续留意')
  } catch (error) {
    console.error('导出失败:', error)
  }
}

// 查看详情
const viewDetail = (id: number) => {
  detailDialog.value.open(id)
}

// 人工执行注销
const manualCancel = (id: number) => {
  ElMessageBox.confirm('确定要执行注销操作吗？此操作不可撤销！', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await manualCancelResume({ cancelLogId: id })
      ElMessage.success('注销操作执行成功')
      getList() // 刷新列表
    } catch (error) {
      console.error('人工执行注销失败:', error)
    }
  })
}

// 分页变化
const handlePaginationChange = (page: any) => {
  formData.page = page.page
  formData.pageSize = page.limit
  pagination.page = page.page
  pagination.limit = page.limit
  getList()
}

// 表格排序
const handleSortTable = (sort: any) => {
  if (sort.prop) {
    formData.sortField = sort.prop
    formData.sortOrder = sort.order === 'ascending' ? 'ASC' : 'DESC'
  } else {
    formData.sortField = ''
    formData.sortOrder = ''
  }
  search()
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case 1:
      return 'warning' // 申请中
    case 2:
      return 'info' // 已撤回
    case 3:
      return 'success' // 已完成
    default:
      return ''
  }
}

// 计算表格高度
const getTableHeight = () => {
  nextTick(() => {
    const topContainer = document.getElementById('top-container')
    if (topContainer) {
      const topHeight = topContainer.offsetHeight
      maxTableHeight.value = window.innerHeight - topHeight - 200
    }
  })
}

// 初始化
onMounted(() => {
  getFilterOptions()
  getStatistics()
  getList()
  getTableHeight()
})
</script>

<style lang="scss" scoped>
.box {
  padding: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

.statistics-container {
  display: flex;
  gap: 30px;
  align-items: center;

  .stat-item {
    display: flex;
    align-items: center;

    .stat-label {
      color: #666;
      margin-right: 8px;
    }

    .stat-value {
      font-weight: bold;
      color: #409eff;
      font-size: 16px;
    }
  }
}

.text-gray-500 {
  color: #9ca3af;
}

.text-sm {
  font-size: 12px;
}

.text-red-500 {
  color: #ef4444;
}
</style>
