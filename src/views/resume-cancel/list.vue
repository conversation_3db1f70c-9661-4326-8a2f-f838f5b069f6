<template>
  <div class="box">
    <div id="top-container" class="pb-15">
      <Filter @search="handleSearch" @toggleShowMore="getTableHeight">
        <template #left>
          <el-button @click="exportData" type="success" class="mr-15">导出数据</el-button>
        </template>
      </Filter>

      <div class="jc-end mt-15 amount">
        <el-link :underline="false" type="primary" @click="handleOpenCustomColumn">选择列</el-link>
      </div>
    </div>

    <el-table
      ref="table"
      :data="list"
      border
      :max-height="maxTableHeight"
      v-loading="loading"
      @sort-change="handleSortTable"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" show-overflow-tooltip></el-table-column>
      <template v-for="item in customColumns">
        <el-table-column
          v-if="item.select"
          :key="item.key"
          :prop="item.prop"
          align="center"
          header-align="center"
          :label="item.label"
          :sortable="item.sortable"
          :min-width="setColumnMinWidth(item.key)"
          :fixed="item.key === 'operation' ? 'right' : false"
          :width="item.key === 'operation' ? '200px' : 'auto'"
        >
          <template v-if="item.slot === 'userInfo'" #default="{ row }">
            <div>
              <div>{{ row.name }}</div>
              <div class="text-gray-500 text-sm">{{ row.mobileMasked }}</div>
              <div class="text-gray-500 text-sm">{{ row.emailMasked }}</div>
            </div>
          </template>

          <template v-else-if="item.slot === 'status'" #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ row.statusText }}
            </el-tag>
          </template>

          <template v-else-if="item.slot === 'remainingDays'" #default="{ row }">
            <span :class="{ 'text-red-500': row.remainingCooldownDays <= 0 }">
              {{ row.remainingCooldownDays }}天
            </span>
          </template>

          <template v-else-if="item.slot === 'operation'" #default="{ row }">
            <div class="flex jc-center ai-center">
              <el-button size="small" type="primary" @click="viewDetail(row.id)">
                查看详情
              </el-button>
              <el-button
                v-if="row.status === 1"
                size="small"
                type="danger"
                @click="manualCancel(row.id)"
              >
                人工执行注销
              </el-button>
            </div>
          </template>
        </el-table-column>
      </template>

      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>

    <div
      id="bottom-container"
      v-show="pagination.total > 0"
      class="pt-15 jc-between ai-center"
      style="flex-shrink: 0"
    >
      <div class="ai-center">
        <el-checkbox
          v-model="checkedAll"
          @change="handleChange"
          class="mr-10"
          label="全选"
        ></el-checkbox>
        <el-select v-model="batchType" :disabled="!multipleSelection.length" filterable clearable>
          <el-option :value="1" label="批量导出"></el-option>
        </el-select>
      </div>
      <Pagination
        v-if="pagination.total > 0"
        :total="pagination.total"
        :page="formData.page"
        @change="handlePaginationChange"
      />
    </div>
  </div>
  <CustomColumnDialog ref="customColumnDialog" v-model:data="customColumns" />
  <DetailDialog ref="detailDialog" @refresh="getList" />
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeMount, onActivated, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Filter from './components/Filter.vue'
import CustomColumnDialog from '/@/components/business/customColumnDialog-V2.vue'
import Pagination from '/@/components/base/paging.vue'
import DetailDialog from './components/DetailDialog.vue'

import {
  getResumeCancelList,
  exportResumeCancelList,
  manualCancelResume
} from '/@/api/resumeCancel'

defineOptions({ name: 'resumeCancelList' })

interface FormData {
  cancelLogId: string
  mobile: string
  name: string
  email: string
  status: string
  cancelReasonType: string
  smsStatus: string
  applyTimeStart: string
  applyTimeEnd: string
  cooldownEndTimeStart: string
  cooldownEndTimeEnd: string
  completeTimeStart: string
  completeTimeEnd: string
  adminId: string
  ip: string
  sortField: string
  sortOrder: string
  page: number
  pageSize: number
}

const customColumnDialog = ref()
const table = ref()
const detailDialog = ref()
const maxTableHeight = ref(450)

const loading = ref(false)
const isFirst = ref(true)
const list = ref([])
const checkedAll = ref(false)
const batchType = ref('')
const multipleSelection = ref([])

// 表单数据
const formData = reactive<FormData>({
  cancelLogId: '',
  mobile: '',
  name: '',
  email: '',
  status: '',
  cancelReasonType: '',
  smsStatus: '',
  applyTimeStart: '',
  applyTimeEnd: '',
  cooldownEndTimeStart: '',
  cooldownEndTimeEnd: '',
  completeTimeStart: '',
  completeTimeEnd: '',
  adminId: '',
  ip: '',
  sortField: '',
  sortOrder: '',
  page: 1,
  pageSize: 20
})

// 分页数据
const pagination = reactive({
  total: 0,
  page: 1,
  limit: 20
})

// 自定义列配置
const customColumns = ref([
  { key: 'id', prop: 'id', label: '注销日志ID', select: true, sortable: true },
  { key: 'userInfo', prop: 'userInfo', label: '用户信息', select: true, slot: 'userInfo' },
  { key: 'status', prop: 'status', label: '注销状态', select: true, slot: 'status' },
  { key: 'cancelReasonTypeText', prop: 'cancelReasonTypeText', label: '注销原因', select: true },
  {
    key: 'applyTimeFormat',
    prop: 'applyTimeFormat',
    label: '申请时间',
    select: true,
    sortable: true
  },
  {
    key: 'cooldownEndTimeFormat',
    prop: 'cooldownEndTimeFormat',
    label: '冷静期结束时间',
    select: true,
    sortable: true
  },
  {
    key: 'remainingDays',
    prop: 'remainingCooldownDays',
    label: '剩余冷静期',
    select: true,
    slot: 'remainingDays'
  },
  { key: 'smsStatusText', prop: 'smsStatusText', label: '短信状态', select: true },
  { key: 'ip', prop: 'ip', label: 'IP地址', select: true },
  { key: 'operation', prop: 'operation', label: '操作', select: true, slot: 'operation' }
])

// 获取列表数据
const getList = async () => {
  loading.value = true

  const params = {}
  Object.keys(formData).forEach((key: string) => {
    const value = formData[key]
    if (value !== '' && value !== null && value !== undefined) {
      params[key] = Array.isArray(value) ? value.join() : value
    }
  })

  try {
    const { list: dataList, pages } = await getResumeCancelList(params)
    list.value = dataList
    pagination.total = pages.total
    pagination.page = pages.page
    pagination.limit = pages.pageSize
  } catch (error) {
    console.error('获取注销申请列表失败:', error)
  } finally {
    loading.value = false
    getTableHeight()
  }
}

// 搜索处理
const handleSearch = (searchData: any) => {
  Object.assign(formData, searchData)
  formData.page = 1
  pagination.page = 1
  getList()
}

// 导出数据
const exportData = async () => {
  try {
    const params = {}
    Object.keys(formData).forEach((key: string) => {
      const value = formData[key]
      if (value !== '' && value !== null && value !== undefined) {
        params[key] = Array.isArray(value) ? value.join() : value
      }
    })

    await exportResumeCancelList(params)
    ElMessage.success('数据开始导出，成功下载后会在企业微信通知，请后续留意')
  } catch (error) {
    console.error('导出失败:', error)
  }
}

// 查看详情
const viewDetail = (id: number) => {
  detailDialog.value.open(id)
}

// 人工执行注销
const manualCancel = (id: number) => {
  ElMessageBox.confirm('确定要执行注销操作吗？此操作不可撤销！', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await manualCancelResume({ cancelLogId: id })
      ElMessage.success('注销操作执行成功')
      getList() // 刷新列表
    } catch (error) {
      console.error('人工执行注销失败:', error)
    }
  })
}

// 分页变化
const handlePaginationChange = (page: any) => {
  formData.page = page.page
  formData.pageSize = page.limit
  pagination.page = page.page
  pagination.limit = page.limit
  getList()
}

// 表格排序
const handleSortTable = (sort: any) => {
  if (sort.prop) {
    formData.sortField = sort.prop
    formData.sortOrder = sort.order === 'ascending' ? 'ASC' : 'DESC'
  } else {
    formData.sortField = ''
    formData.sortOrder = ''
  }
  handleSearch(formData)
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection
}

// 全选变化
const handleChange = (val: boolean) => {
  if (val) {
    table.value.toggleAllSelection()
  } else {
    table.value.clearSelection()
  }
}

// 打开自定义列
const handleOpenCustomColumn = () => {
  customColumnDialog.value.open()
}

// 设置列最小宽度
const setColumnMinWidth = (key: string) => {
  const minWidthMap = {
    id: 120,
    userInfo: 200,
    status: 100,
    cancelReasonTypeText: 200,
    applyTimeFormat: 160,
    cooldownEndTimeFormat: 160,
    remainingDays: 120,
    smsStatusText: 140,
    ip: 120,
    operation: 200
  }
  return minWidthMap[key] || 100
}

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case 1:
      return 'warning' // 申请中
    case 2:
      return 'info' // 已撤回
    case 3:
      return 'success' // 已完成
    default:
      return ''
  }
}

// 计算表格高度
const getTableHeight = () => {
  nextTick(() => {
    const topContainer = document.getElementById('top-container')
    if (topContainer) {
      const topHeight = topContainer.offsetHeight
      maxTableHeight.value = window.innerHeight - topHeight - 200
    }
  })
}

// 初始化
onBeforeMount(() => {
  getList()
})

onActivated(() => {
  if (!isFirst.value) {
    getList()
  }
  isFirst.value = false
})
</script>

<style scoped lang="scss">
:deep() {
  @import '/@/theme/tableScrollBar.scss';
}

.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;

  a {
    text-decoration: none;
    color: var(--color-primary);
  }
}

.amount {
  margin: 20px 0 0px;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;
}

.text-gray-500 {
  color: #9ca3af;
}

.text-sm {
  font-size: 12px;
}

.text-red-500 {
  color: #ef4444;
}
</style>
