<template>
  <div>
    <el-dialog v-model="jobFileUploadVisbled">
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        multiple
        :http-request="handleUpload"
        :limit="10"
        :show-file-list="false"
        v-loading="fileLoading"
        :on-exceed="fileExceed"
        :file-list="fileList"
      >
        <div>请上传doc、docx、xls、xlsx、pdf、rar、zip、ppt、jpg、jpeg、png、txt类型的文件</div>
        <div class="el-upload__text">点击或将文件拖拽到这里上传</div>
        <div class="el-upload__text">上传附件，最大20M</div>
      </el-upload>
    </el-dialog>
    <div class="file-list" ref="fileListRef">
      <div class="file" v-for="(item, index) in fileList" :key="index" :data-id="item.id">
        <div class="drag-handle">≡</div>
        <div>{{ item.name }}</div>
        <span @click="removeFile(item.id)">X</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { ElMessage } from 'element-plus'
import { computed, defineComponent, reactive, ref, toRefs, onMounted, nextTick, toRef } from 'vue'
import Sortable from 'sortablejs'

export default defineComponent({
  name: 'jobFileUpload',

  props: {
    modelValue: {
      type: Array,
      default: () => {}
    }
  },

  setup(props, { emit }) {
    const state = reactive({
      jobFileUploadVisbled: false,
      fileLoading: false,
      fileList: <any>computed({
        get() {
          return props.modelValue.length ? props.modelValue : []
        },
        set(val) {
          emit('update:modelValue', val)
        }
      })
    })

    const fileListRef = ref(null)

    const openJobFileUpload = () => {
      state.jobFileUploadVisbled = true
    }

    const uploadRef = ref()

    const jobFileSuccess = (res: any, file: any, fileList: any[]) => {
      state.fileLoading = false
      if (res.result === 1) {
        // 不关闭弹窗，允许继续上传
        // state.jobFileUploadVisbled = false
        const newFile = { name: res.data.name, id: res.data.id }
        state.fileList = [...state.fileList, newFile]
      } else {
        uploadRef.value.clearFiles()
        ElMessage.error(res.msg)
      }
    }
    const fileOnProgress = () => {
      state.fileLoading = true
    }

    const fileExceed = () => {
      ElMessage.warning('职位附件不能超过10个')
    }

    const removeFile = async (id: string) => {
      state.fileList = state.fileList.filter((item: any) => item.id !== id)
    }
    const clear = () => {
      state.fileList = []
    }

    const beforeFileUpload = (rawFile: any) => {
      if (rawFile.size / 1024 / 1024 > 20) {
        ElMessage.error('文件过大')
        return false
      }
      return true
    }

    const uploadFile = async (file: any) => {
      const formData = new FormData()
      formData.append('file', file)

      try {
        const response = await fetch('/upload/job-appendix', {
          method: 'POST',
          body: formData
        })
        const res = await response.json()

        if (res.result === 1) {
          const newFile = { name: res.data.name, id: res.data.id }
          state.fileList = [...state.fileList, newFile]
          return true
        }
        ElMessage.error(res.msg)
        return false
      } catch (error) {
        ElMessage.error('上传失败')
        return false
      }
    }

    const handleUpload = async ({ file }: { file: File }) => {
      state.fileLoading = true
      try {
        await uploadFile(file)
      } finally {
        state.fileLoading = false
        state.jobFileUploadVisbled = false
      }
    }

    onMounted(() => {
      nextTick(() => {
        if (fileListRef.value) {
          new Sortable(fileListRef.value, {
            animation: 150,
            handle: '.drag-handle',
            onEnd({ oldIndex, newIndex }) {
              const itemsCopy = [...state.fileList]
              const [removed] = itemsCopy.splice(oldIndex, 1)
              itemsCopy.splice(newIndex, 0, removed)
              // 强制更新数组引用以触发响应式更新
              state.fileList = []
              nextTick(() => {
                state.fileList = itemsCopy
              })
            }
          })
        }
      })
    })

    return {
      ...toRefs(state),
      fileListRef,
      openJobFileUpload,
      jobFileSuccess,
      removeFile,
      clear,
      fileOnProgress,
      fileExceed,
      uploadRef,
      beforeFileUpload,
      handleUpload
    }
  }
})
</script>

<style lang="scss" scoped>
.file-list {
  .file {
    display: flex;
    align-items: center;
    margin: 10px 0;
    text-decoration: none;
    color: #409eff;

    .drag-handle {
      cursor: move;
      margin-right: 10px;
      color: #999;
    }

    > div:nth-child(2) {
      flex: 1;
    }

    span {
      cursor: pointer;
    }
  }
}
.upload-demo {
  text-align: center;
  :deep(.el-upload-dragger) {
    display: flex;
    flex-direction: column;
    justify-content: center;
    div {
      margin: 5px 0;
    }
  }
  .el-button {
    margin-top: 20px;
  }
}
.dialog-footer {
  margin-top: 20px;
  text-align: center;
}
</style>
