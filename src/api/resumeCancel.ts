import request from '/@/utils/request'

/**
 * 获取注销申请列表
 * @param params 查询参数
 * @returns 返回注销申请列表数据
 */
export function getResumeCancelList(params: object) {
  return request({
    url: '/resume-cancel/get-list',
    method: 'get',
    params
  })
}

/**
 * 获取注销申请详情
 * @param params 查询参数
 * @returns 返回注销申请详情数据
 */
export function getResumeCancelDetail(params: object) {
  return request({
    url: '/resume-cancel/get-detail',
    method: 'get',
    params
  })
}

/**
 * 获取统计数据
 * @param params 查询参数
 * @returns 返回统计数据
 */
export function getResumeCancelStatistics(params: object) {
  return request({
    url: '/resume-cancel/get-statistics',
    method: 'get',
    params
  })
}

/**
 * 获取筛选选项
 * @returns 返回筛选选项数据
 */
export function getResumeCancelFilterOptions() {
  return request({
    url: '/resume-cancel/get-filter-options',
    method: 'get'
  })
}

/**
 * 人工执行注销
 * @param params 请求参数
 * @returns 返回执行结果
 */
export function manualCancelResume(params: object) {
  return request({
    url: '/resume-cancel/manual-cancel',
    method: 'post',
    data: params
  })
}

/**
 * 导出注销申请列表
 * @param params 查询参数
 * @returns 返回导出结果
 */
export function exportResumeCancelList(params: object) {
  return request({
    url: '/resume-cancel/export',
    method: 'get',
    params
  })
}
