import { RouteRecordRaw } from 'vue-router'

export const resumeCancelRoutes: Array<RouteRecordRaw> = [
  {
    path: '/resume-cancel',
    name: 'resumeCancel',
    component: () => import('/@/layout/routerView/parent.vue'),
    meta: {
      title: '注销管理',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/person.svg'
    },
    children: [
      {
        path: '/resume-cancel/list',
        name: 'resumeCancelList',
        component: () => import('/@/views/resume-cancel/list.vue'),
        meta: {
          title: '注销申请列表',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/resume-cancel/detail/:id',
        name: 'resumeCancelDetail',
        component: () => import('/@/views/resume-cancel/detail.vue'),
        meta: {
          title: '注销申请详情',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
